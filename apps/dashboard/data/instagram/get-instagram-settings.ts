import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { InstagramSettingsDto } from '~/types/dtos/instagram-settings-dto';

export async function getInstagramSettings(): Promise<InstagramSettingsDto | null> {
  const ctx = await getAuthOrganizationContext();

  const settings = await prisma.instagramSettings.findFirst({
        where: { organizationId: ctx.organization.id },
        select: {
          id: true,
          isBotEnabled: true,
          minResponseTime: true,
          maxResponseTime: true,
          messageDelayMin: true,
          messageDelayMax: true,
          instagramToken: true,
          instagramAccountId: true,
          instagramUsername: true,
          instagramName: true,
          instagramProfilePicture: true,
          isConnected: true,
          tokenExpiresAt: true,
          autoCleanupEnabled: true,
          followUpCleanupDays: true
        }
      });

      if (!settings) {
        return null;
      }

      return {
        id: settings.id,
        isBotEnabled: settings.isBotEnabled,
        minResponseTime: settings.minResponseTime,
        maxResponseTime: settings.maxResponseTime,
        messageDelayMin: settings.messageDelayMin,
        messageDelayMax: settings.messageDelayMax,
        instagramToken: settings.instagramToken || undefined,
        instagramAccountId: settings.instagramAccountId || undefined,
        instagramUsername: settings.instagramUsername || undefined,
        instagramName: settings.instagramName || undefined,
        instagramProfilePicture: settings.instagramProfilePicture || undefined,
        isConnected: settings.isConnected || false,
        tokenExpiresAt: settings.tokenExpiresAt || undefined,
        autoCleanupEnabled: settings.autoCleanupEnabled ?? undefined,
        followUpCleanupDays: settings.followUpCleanupDays ?? undefined
      };

  return {
    id: settings.id,
    isBotEnabled: settings.isBotEnabled,
    minResponseTime: settings.minResponseTime,
    maxResponseTime: settings.maxResponseTime,
    messageDelayMin: settings.messageDelayMin,
    messageDelayMax: settings.messageDelayMax,
    instagramToken: settings.instagramToken || undefined,
    instagramAccountId: settings.instagramAccountId || undefined,
    instagramUsername: settings.instagramUsername || undefined,
    instagramName: settings.instagramName || undefined,
    instagramProfilePicture: settings.instagramProfilePicture || undefined,
    isConnected: settings.isConnected,
    tokenExpiresAt: settings.tokenExpiresAt || undefined,
    autoCleanupEnabled: settings.autoCleanupEnabled,
    followUpCleanupDays: settings.followUpCleanupDays
  };
}
