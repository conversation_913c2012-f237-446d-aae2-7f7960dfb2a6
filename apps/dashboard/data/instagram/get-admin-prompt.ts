import 'server-only';

import { prisma } from '@workspace/database/client';
import { AdminPromptDto } from '~/types/dtos/instagram/admin-prompt-dto';

export async function getAdminPrompt(): Promise<AdminPromptDto | null> {
  const adminPrompt = await prisma.adminPrompt.findFirst({
        select: {
          id: true,
          generalPrompt: true,
          technicalPrompt: true,
          conversationGatheringPrompt: true
        }
      });

      if (!adminPrompt) {
        return null;
      }

      return {
        id: adminPrompt.id,
        generalPrompt: adminPrompt.generalPrompt,
        technicalPrompt: adminPrompt.technicalPrompt,
        conversationGatheringPrompt: adminPrompt.conversationGatheringPrompt || undefined
      };

  if (!adminPrompt) {
    return null;
  }

  return {
    id: adminPrompt.id,
    generalPrompt: adminPrompt.generalPrompt,
    technicalPrompt: adminPrompt.technicalPrompt,
    conversationGatheringPrompt: adminPrompt.conversationGatheringPrompt || undefined
  };
}
