import 'server-only';

import { prisma } from '@workspace/database/client';
import { AdminSettingsDto } from '~/types/dtos/admin-settings-dto';

export async function getAdminSettings(): Promise<AdminSettingsDto | null> {
  const adminSettings = await prisma.adminSettings.findFirst({
        select: {
          id: true,
          cacheForAllUsers: true,
          cacheType: true,
          enableConversationCache: true,
          maxConversationMessages: true,
          aiProvider: true,
          openrouterApiKey: true,
          openrouterModel: true,
          useReasoning: true,
          reasoningBudget: true
        }
      });

      if (!adminSettings) {
        return null;
      }

      return {
        id: adminSettings.id,
        cacheForAllUsers: adminSettings.cacheForAllUsers,
        cacheType: (adminSettings.cacheType as '5m' | '1h') || '5m',
        enableConversationCache: adminSettings.enableConversationCache,
        maxConversationMessages: adminSettings.maxConversationMessages,
        aiProvider: (adminSettings.aiProvider as 'claude' | 'openrouter') || 'claude',
        openrouterApiKey: adminSettings.openrouterApiKey || undefined,
        openrouterModel: adminSettings.openrouterModel || undefined,
        useReasoning: adminSettings.useReasoning,
        reasoningBudget: adminSettings.reasoningBudget
      };

  if (!adminSettings) {
    return null;
  }

  return {
    id: adminSettings.id,
    cacheForAllUsers: adminSettings.cacheForAllUsers,
    cacheType: (adminSettings.cacheType as '5m' | '1h') || '5m',
    enableConversationCache: adminSettings.enableConversationCache,
    maxConversationMessages: adminSettings.maxConversationMessages,
    aiProvider: (adminSettings.aiProvider as 'claude' | 'openrouter') || 'claude',
    openrouterApiKey: adminSettings.openrouterApiKey || undefined,
    openrouterModel: adminSettings.openrouterModel || undefined,
    useReasoning: adminSettings.useReasoning,
    reasoningBudget: adminSettings.reasoningBudget
  };
}
