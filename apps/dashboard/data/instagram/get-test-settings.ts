import 'server-only';

import { prisma } from '@workspace/database/client';

import { TestSettingsDto } from '~/types/dtos/test-settings-dto';

export async function getTestSettings(organizationId: string, userId: string): Promise<TestSettingsDto | null> {
  const testSettings = await prisma.testSettings.findFirst({
        where: { 
          organizationId,
          userId 
        },
        select: {
          id: true,
          organizationId: true,
          userId: true,
          aboutUs: true,
          qualificationQuestions: true,
          additionalInfo: true,
          botStyleId: true,
          youtubeLink: true,
          websiteLink: true,
          leadMagnetLink: true,
          conversionLink: true,
          botStyle: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      if (!testSettings) {
        return null;
      }

      return {
        id: testSettings.id,
        organizationId: testSettings.organizationId,
        userId: testSettings.userId,
        aboutUs: testSettings.aboutUs || undefined,
        qualificationQuestions: testSettings.qualificationQuestions || undefined,
        additionalInfo: testSettings.additionalInfo || undefined,
        botStyleId: testSettings.botStyleId || undefined,
        botStyleName: testSettings.botStyle?.name || undefined,
        youtubeLink: testSettings.youtubeLink || undefined,
        websiteLink: testSettings.websiteLink || undefined,
        leadMagnetLink: testSettings.leadMagnetLink || undefined,
        conversionLink: testSettings.conversionLink || undefined
      };

  if (!testSettings) {
    return null;
  }

  return {
    id: testSettings.id,
    organizationId: testSettings.organizationId,
    userId: testSettings.userId,
    aboutUs: testSettings.aboutUs || undefined,
    qualificationQuestions: testSettings.qualificationQuestions || undefined,
    additionalInfo: testSettings.additionalInfo || undefined,
    botStyleId: testSettings.botStyleId || undefined,
    botStyleName: testSettings.botStyle?.name || undefined,
    youtubeLink: testSettings.youtubeLink || undefined,
    websiteLink: testSettings.websiteLink || undefined,
    leadMagnetLink: testSettings.leadMagnetLink || undefined,
    conversionLink: testSettings.conversionLink || undefined
  };
}
