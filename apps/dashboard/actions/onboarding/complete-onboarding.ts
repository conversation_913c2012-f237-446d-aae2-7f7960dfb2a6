'use server';

import { createHash } from 'crypto';
import { revalidateTag } from 'next/cache';
import { v4 } from 'uuid';

import {
  checkIfCanInvite,
  createInvitation,
  sendInvitationRequest
} from '@workspace/auth/invitations';
import {
  addOrganizationToStripe,
  updateOrganizationSubscriptionQuantity
} from '@workspace/billing/organization';
import { Tier } from '@workspace/billing/tier';
import type { Maybe } from '@workspace/common/maybe';
import {
  DayOfWeek,
  InvitationStatus,
  Role,
  type Prisma
} from '@workspace/database';
import { prisma } from '@workspace/database/client';
import { decodeBase64Image } from '@workspace/image-processing/decode-base64-image';
import { resizeImage } from '@workspace/image-processing/resize-image';
import {
  getOrganizationLogoUrl,
  getUserImageUrl,
  replaceOrgSlug,
  routes
} from '@workspace/routes';


import { authActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCache<PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON><PERSON> } from '~/data/caching';
import { FileUploadAction } from '~/lib/file-upload';
import { getTimeSlot } from '~/lib/formatters';
import {
  completeOnboardingSchema,
  OnboardingStep,
  type CompleteOnboardingSchema
} from '~/schemas/onboarding/complete-onboarding-schema';

type Transaction = Prisma.PrismaPromise<unknown>;

export const completeOnboarding = authActionClient
  .metadata({ actionName: 'completeOnboarding' })
  .schema(completeOnboardingSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      console.log('🚀 Starting onboarding process for user:', ctx.session.user.id);
      const transactions: Transaction[] = [];
      const organizationId = v4();
      const userId = ctx.session.user.id;
      const userEmail = ctx.session.user.email.toLowerCase();

      console.log('📝 Processing onboarding steps:', parsedInput.activeSteps);

      // Handle profile step
      if (parsedInput.activeSteps.includes(OnboardingStep.Profile)) {
        console.log('👤 Handling profile step');
        await handleProfileStep(parsedInput.profileStep, userId, transactions);
      }

      // Handle theme step
      // No action required for theme step

      // Handle organization step
      if (parsedInput.activeSteps.includes(OnboardingStep.Organization)) {
        console.log('🏢 Handling organization step');
        await handleOrganizationStep(
          parsedInput.organizationStep,
          organizationId,
          userEmail,
          userId,
          transactions
        );
      }

      // Handle pending invitations step
      if (parsedInput.activeSteps.includes(OnboardingStep.PendingInvitations)) {
        console.log('📨 Handling pending invitations step');
        await handlePendingInvitationsStep(
          parsedInput.pendingInvitationsStep,
          userId,
          userEmail,
          transactions
        );
      }

      if (transactions.length) {
        console.log(`💾 Executing ${transactions.length} database transactions`);
        try {
          await prisma.$transaction(transactions);
          console.log('✅ Database transactions completed successfully');
        } catch (error) {
          console.error('❌ Database transaction failed:', error);
          throw new Error(`Database transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      console.log('🔄 Revalidating cache tags');
      revalidateTag(Caching.createUserTag(UserCacheKey.PersonalDetails, userId));
      revalidateTag(Caching.createUserTag(UserCacheKey.Preferences, userId));
      revalidateTag(Caching.createUserTag(UserCacheKey.Organizations, userId));

      // Ideally we would execute these in a background job
      if (
        parsedInput.activeSteps.includes(OnboardingStep.Organization) &&
        parsedInput.organizationStep
      ) {
        // Handle invite team step
        if (parsedInput.activeSteps.includes(OnboardingStep.InviteTeam)) {
          console.log('👥 Handling invite team step');
          await handleInviteTeamStep(
            parsedInput.inviteTeamStep,
            organizationId,
            parsedInput.organizationStep.name,
            ctx.session.user.name,
            ctx.session.user.email
          );
        }

        // Note: Example data functionality removed as it was Contact-related
      }

      console.log('🔍 Fetching user memberships');
      const memberships = await prisma.membership.findMany({
        where: { userId: ctx.session.user.id },
        select: { organization: { select: { id: true, slug: true } } }
      });

      console.log(`👥 Found ${memberships.length} memberships`);

      for (const membership of memberships) {
        try {
          await updateOrganizationSubscriptionQuantity(
            membership.organization.id
          );
        } catch (e) {
          console.error('Failed to update subscription quantity:', e);
          // Continue without updating subscription
        }

        revalidateTag(
          Caching.createOrganizationTag(
            OrganizationCacheKey.Members,
            membership.organization.id
          )
        );
        revalidateTag(
          Caching.createOrganizationTag(
            OrganizationCacheKey.Invitations,
            membership.organization.id
          )
        );
      }

      let redirect: string = routes.dashboard.organizations.Index;
      if (memberships.length > 0) {
        redirect = replaceOrgSlug(
          routes.dashboard.organizations.slug.Home,
          memberships[0].organization.slug
        );
      }

      console.log('🎉 Onboarding completed successfully, redirecting to:', redirect);
      return { redirect };
    } catch (error) {
      console.error('Onboarding failed:', error);
      throw new Error(`Onboarding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });

async function handleProfileStep(
  step: CompleteOnboardingSchema['profileStep'],
  userId: string,
  transactions: Transaction[]
) {
  if (!step) {
    return;
  }

  let imageUrl: Maybe<string> = undefined;
  if (step.action === FileUploadAction.Update && step.image) {
    const { buffer, mimeType } = decodeBase64Image(step.image);
    const data = await resizeImage(buffer, mimeType);
    const hash = createHash('sha256').update(data).digest('hex');

    transactions.push(
      prisma.userImage.deleteMany({ where: { userId } }),
      prisma.userImage.create({
        data: { userId, data, contentType: mimeType, hash }
      })
    );

    imageUrl = getUserImageUrl(userId, hash);
  }
  if (step.action === FileUploadAction.Delete) {
    transactions.push(prisma.userImage.deleteMany({ where: { userId } }));
    imageUrl = null;
  }

  // Update user profile
  transactions.push(
    prisma.user.update({
      where: { id: userId },
      data: {
        image: imageUrl,
        name: step.name,
        phone: step.phone,
        completedOnboarding: true
      }
    })
  );
}

// Validation function for organization data
function validateOrganizationData(step: CompleteOnboardingSchema['organizationStep']): void {
  if (!step) {
    throw new Error('Organization step data is required');
  }

  if (!step.name || step.name.trim().length === 0) {
    throw new Error('Organization name is required');
  }

  if (step.name.length > 64) {
    throw new Error('Organization name must be 64 characters or less');
  }

  if (!step.slug || step.slug.trim().length === 0) {
    throw new Error('Organization slug is required');
  }

  if (step.slug.length < 3) {
    throw new Error('Organization slug must be at least 3 characters long');
  }

  if (step.slug.length > 255) {
    throw new Error('Organization slug must be 255 characters or less');
  }

  // Validate slug format: only lowercase letters, numbers, hyphens, and underscores
  const slugRegex = /^[a-z0-9]+[a-z0-9_-]*[a-z0-9]+$/;
  if (!slugRegex.test(step.slug)) {
    throw new Error('Organization slug must start and end with a letter or number and can contain only lowercase letters, numbers, hyphens, and underscores');
  }
}

async function handleOrganizationStep(
  step: CompleteOnboardingSchema['organizationStep'],
  organizationId: string,
  userEmail: string,
  userId: string,
  transactions: Transaction[]
) {
  if (!step) {
    console.log('⚠️ No organization step data provided');
    return;
  }

  console.log('🏢 Creating organization:', { name: step.name, slug: step.slug, organizationId });

  // Validate organization data
  try {
    validateOrganizationData(step);
    console.log('✅ Organization data validation passed');
  } catch (error) {
    console.error('❌ Organization data validation failed:', error);
    throw error;
  }

  let stripeCustomerId: string | null = null;
  try {
    console.log('💳 Creating Stripe customer...');
    stripeCustomerId = await addOrganizationToStripe(
      step.name,
      userEmail,
      organizationId
    );
    console.log('✅ Stripe customer created:', stripeCustomerId);
  } catch (error) {
    console.warn('⚠️ Failed to create Stripe customer:', error);
    // Continue without Stripe customer ID - organization can be created without it
  }

  let logoUrl: Maybe<string> = undefined;
  if (step.logo) {
    try {
      console.log('🖼️ Processing organization logo...');
      const { buffer, mimeType } = decodeBase64Image(step.logo);
      const data = await resizeImage(buffer, mimeType);
      const hash = createHash('sha256').update(data).digest('hex');
      transactions.push(
        prisma.organizationLogo.create({
          data: {
            organizationId,
            data,
            contentType: mimeType,
            hash
          },
          select: {
            id: true // SELECT NONE
          }
        })
      );

      logoUrl = getOrganizationLogoUrl(organizationId, hash);
      console.log('✅ Logo processed successfully');
    } catch (error) {
      console.warn('⚠️ Failed to process logo:', error);
      // Continue without logo - organization can be created without it
    }
  }

  // Create organization with proper error handling
  try {
    console.log('💾 Adding organization creation to transactions...');
    transactions.push(
      prisma.organization.create({
        data: {
          id: organizationId,
          logo: logoUrl,
          name: step.name,
          stripeCustomerId: stripeCustomerId, // Now properly nullable
          slug: step.slug,
          tier: Tier.Free,
          memberships: {
            create: {
              userId,
              role: Role.ADMIN,
              isOwner: true
            }
          }
        }
      })
    );
    console.log('✅ Organization creation added to transactions');
  } catch (error) {
    console.error('❌ Failed to create organization:', error);
    throw new Error(`Failed to create organization: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

async function handlePendingInvitationsStep(
  step: CompleteOnboardingSchema['pendingInvitationsStep'],
  userId: string,
  userEmail: string,
  transactions: Transaction[]
): Promise<void> {
  if (!step || !step.invitationIds) {
    return;
  }

  for (const invitationId of step.invitationIds) {
    const pendingInvitation = await prisma.invitation.findFirst({
      where: {
        id: invitationId,
        email: userEmail,
        status: InvitationStatus.PENDING
      },
      select: {
        organizationId: true,
        role: true
      }
    });
    if (!pendingInvitation) {
      continue;
    }

    transactions.push(
      prisma.membership.create({
        data: {
          userId,
          organizationId: pendingInvitation.organizationId,
          role: pendingInvitation.role
        }
      }),
      prisma.invitation.update({
        where: { id: invitationId },
        data: { status: InvitationStatus.ACCEPTED }
      })
    );
  }
}

async function handleInviteTeamStep(
  step: CompleteOnboardingSchema['inviteTeamStep'],
  organizationId: string,
  organizationName: string,
  userName: string,
  userEmail: string
): Promise<void> {
  if (!step || !step.invitations) {
    return;
  }

  for (const invitation of step.invitations) {
    if (!invitation.email) {
      continue;
    }

    const canInvite = await checkIfCanInvite(invitation.email, organizationId);
    if (!canInvite) {
      continue;
    }

    try {
      const newInvitation = await createInvitation(
        invitation.email,
        invitation.role,
        organizationId
      );
      await sendInvitationRequest({
        email: newInvitation.email,
        organizationName,
        invitedByEmail: userEmail,
        invitedByName: userName,
        token: newInvitation.token,
        invitationId: newInvitation.id,
        organizationId
      });
    } catch (e) {
      console.error(e);
    }
  }
}

function createDefaultBusinessHours() {
  try {
    console.log('🕐 Creating default business hours...');
    
    // Create a standard 9 AM to 5 PM time slot
    const timeSlot = { 
      start: getTimeSlot(9, 0), 
      end: getTimeSlot(17, 0) 
    };
    
    console.log('⏰ Default time slot:', { 
      start: timeSlot.start.toTimeString(), 
      end: timeSlot.end.toTimeString() 
    });

    const businessHours = {
      create: [
        { dayOfWeek: DayOfWeek.SUNDAY },
        { dayOfWeek: DayOfWeek.MONDAY, timeSlots: { create: timeSlot } },
        { dayOfWeek: DayOfWeek.TUESDAY, timeSlots: { create: timeSlot } },
        { dayOfWeek: DayOfWeek.WEDNESDAY, timeSlots: { create: timeSlot } },
        { dayOfWeek: DayOfWeek.THURSDAY, timeSlots: { create: timeSlot } },
        { dayOfWeek: DayOfWeek.FRIDAY, timeSlots: { create: timeSlot } },
        { dayOfWeek: DayOfWeek.SATURDAY }
      ]
    };

    console.log('✅ Default business hours created successfully');
    return businessHours;
  } catch (error) {
    console.error('❌ Failed to create default business hours:', error);
    
    // Return minimal business hours structure as fallback
    try {
      const fallbackTimeSlot = { 
        start: getTimeSlot(9, 0), 
        end: getTimeSlot(17, 0) 
      };
      
      console.log('🔄 Using fallback business hours structure');
      return {
        create: [
          { dayOfWeek: DayOfWeek.MONDAY, timeSlots: { create: fallbackTimeSlot } }
        ]
      };
    } catch (fallbackError) {
      console.error('❌ Even fallback business hours failed:', fallbackError);
      // Return empty business hours as last resort
      return { create: [] };
    }
  }
}
