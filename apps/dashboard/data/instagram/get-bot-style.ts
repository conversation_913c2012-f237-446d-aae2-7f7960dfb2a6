import 'server-only';

import { prisma } from '@workspace/database/client';
import { BotStyleDto } from '~/types/dtos/instagram/bot-style-dto';

export async function getBotStyle(id: string): Promise<BotStyleDto | null> {
  const botStyle = await prisma.botStyle.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          description: true,
          promptText: true,
          isDefault: true
        }
      });

      if (!botStyle) {
        return null;
      }

      return {
        id: botStyle.id,
        name: botStyle.name,
        description: botStyle.description || undefined,
        promptText: botStyle.promptText,
        isDefault: botStyle.isDefault
      };

  if (!botStyle) {
    return null;
  }

  return {
    id: botStyle.id,
    name: botStyle.name,
    description: botStyle.description || undefined,
    promptText: botStyle.promptText,
    isDefault: botStyle.isDefault
  };
}
