import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { prisma } from '@workspace/database/client';
import { transcribeMedia } from '~/lib/openai-client';
import { queueMessage } from '@workspace/instagram-bot';
import { normalizeTimestamp } from '~/lib/formatters';

// Environment variables
const INSTAGRAM_VERIFY_TOKEN = process.env.INSTAGRAM_VERIFY_TOKEN || 'verifytoken123';
const INSTAGRAM_APP_SECRET = process.env.INSTAGRAM_APP_SECRET || 'your_app_secret';

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Webhook deduplication store (in production, use Redis)
const webhookDedupeStore = new Map<string, number>(); // messageId -> timestamp
const WEBHOOK_DEDUPE_TIMEOUT = 300000; // 5 minutes

// Webhook validation helpers
async function isRateLimited(clientIP: string): Promise<boolean> {
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 100; // Max 100 requests per minute per IP

  const current = rateLimitStore.get(clientIP);

  if (!current || now > current.resetTime) {
    rateLimitStore.set(clientIP, { count: 1, resetTime: now + windowMs });
    return false;
  }

  if (current.count >= maxRequests) {
    return true;
  }

  current.count++;
  return false;
}

async function validateWebhookSignature(signature: string | null, rawBody: string, requestId: string): Promise<boolean> {
  if (!signature) {
    console.log(`[${requestId}] Missing signature`);
    return process.env.NODE_ENV !== 'production';
  }

  try {
    const expectedSignature = 'sha256=' + crypto
      .createHmac('sha256', INSTAGRAM_APP_SECRET)
      .update(rawBody)
      .digest('hex');

    const isValid = signature === expectedSignature;

    if (!isValid) {
      console.log(`[${requestId}] Invalid signature`);
      console.log(`[${requestId}] Expected:`, expectedSignature);
      console.log(`[${requestId}] Received:`, signature);
    }

    return isValid || process.env.NODE_ENV !== 'production';
  } catch (error) {
    console.error(`[${requestId}] Error validating signature:`, error);
    return false;
  }
}

function isValidWebhookPayload(body: any): boolean {
  return (
    body &&
    typeof body === 'object' &&
    body.object === 'instagram' &&
    Array.isArray(body.entry) &&
    body.entry.length > 0
  );
}

/**
 * Handle GET requests for webhook verification
 */
export async function GET(req: NextRequest): Promise<Response> {
  console.log('Received webhook verification request');

  // Get query parameters
  const mode = req.nextUrl.searchParams.get('hub.mode');
  const token = req.nextUrl.searchParams.get('hub.verify_token');
  const challenge = req.nextUrl.searchParams.get('hub.challenge');

  console.log(`Mode: ${mode}, Token: ${token}, Challenge: ${challenge}`);
  console.log(`Expected token: ${INSTAGRAM_VERIFY_TOKEN}`);

  // Verify webhook subscription
  if (mode === 'subscribe' && token === INSTAGRAM_VERIFY_TOKEN) {
    if (!challenge) {
      console.log('Missing challenge parameter');
      return NextResponse.json({ error: 'Missing challenge parameter' }, { status: 400 });
    }
    console.log(`Verification successful, returning challenge: ${challenge}`);
    return new NextResponse(challenge);
  }

  console.log('Invalid verification request');
  return NextResponse.json({ error: 'Invalid verification request' }, { status: 403 });
}

/**
 * Handle POST requests for webhook events
 */
export async function POST(req: NextRequest): Promise<Response> {
  const startTime = Date.now();
  const requestId = `webhook_${startTime}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[${requestId}] Received webhook event`);

    // Rate limiting check
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    if (await isRateLimited(clientIP)) {
      console.log(`[${requestId}] Rate limited request from ${clientIP}`);
      return NextResponse.json({ error: 'Rate limited' }, { status: 429 });
    }

    // Get the raw body for signature validation
    const rawBody = await req.text();

    // Validate JSON structure
    let body;
    try {
      body = JSON.parse(rawBody);
    } catch (parseError) {
      console.error(`[${requestId}] Invalid JSON payload:`, parseError);
      return NextResponse.json({ error: 'Invalid JSON' }, { status: 400 });
    }

    // Enhanced signature validation
    const signature = req.headers.get('x-hub-signature-256');
    const isValidSignature = await validateWebhookSignature(signature, rawBody, requestId);

    if (!isValidSignature && process.env.NODE_ENV === 'production') {
      console.error(`[${requestId}] Invalid webhook signature`);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 403 });
    }

    // Validate webhook structure
    if (!isValidWebhookPayload(body)) {
      console.error(`[${requestId}] Invalid webhook payload structure`);
      return NextResponse.json({ error: 'Invalid payload structure' }, { status: 400 });
    }

    // Log the webhook event
    console.log('Webhook event received:', JSON.stringify(body, null, 2));

    // Process the webhook event
    if (body.object === 'instagram' && body.entry && body.entry.length > 0) {
      for (const entry of body.entry) {
        const instagramBusinessAccountId = entry.id;
        console.log(`Instagram Business Account ID: ${instagramBusinessAccountId}`);

        if (entry.messaging && entry.messaging.length > 0) {
          for (const messaging of entry.messaging) {
            // Skip read receipts and delivery confirmations - only process actual messages
            if (messaging.read) {
              console.log('Skipping read receipt event');
              continue;
            }
            
            if (messaging.delivery) {
              console.log('Skipping delivery confirmation event');
              continue;
            }
            
            if (messaging.sender && messaging.message) {
              // Skip echo messages (messages sent by the page itself)
              if (messaging.message.is_echo) {
                console.log('Skipping echo message');
                continue;
              }

              const senderId = messaging.sender.id;
              const messageId = messaging.message.mid;
              const messageText = messaging.message.text || '';
              // Instagram webhook timestamp is in milliseconds
              const timestamp = messaging.timestamp ? messaging.timestamp : Date.now();

              console.log(`Sender ID: ${senderId}`);
              console.log(`Message ID: ${messageId}`);
              console.log(`Message Text: ${messageText}`);
              console.log(`Timestamp: ${timestamp} (${new Date(timestamp).toISOString()})`);

              // Process attachments if any
              let mediaUrl;
              let mediaType;

              if (messaging.message.attachments && messaging.message.attachments.length > 0) {
                const attachment = messaging.message.attachments[0];
                mediaType = attachment.type;
                mediaUrl = attachment.payload.url;
                console.log(`Media Type: ${mediaType}`);
                console.log(`Media URL: ${mediaUrl}`);
              }

              // Find organization by Instagram business account ID
              const instagramSettings = await prisma.instagramSettings.findFirst({
                where: {
                  isBotEnabled: true,
                  instagramToken: { not: null }
                },
                include: {
                  Organization: {
                    include: {
                      memberships: {
                        where: {
                          role: 'ADMIN'
                        },
                        include: {
                          user: true
                        },
                        take: 1
                      }
                    }
                  }
                }
              });

              if (!instagramSettings) {
                console.error('No organization found with Instagram bot enabled');
                return NextResponse.json({ error: 'No organization configured' }, { status: 400 });
              }

              const organizationId = instagramSettings.organizationId;
              const userId = instagramSettings.Organization.memberships[0]?.user.id;
              console.log(`Organization ID: ${organizationId}`);

              // Enhanced echo prevention: Check if message is from the business account itself
              const isFromBusinessAccount = senderId === instagramBusinessAccountId;
              console.log(`Message is from business account: ${isFromBusinessAccount}`);

              // Additional echo prevention: Check if this sender matches our stored Instagram account ID
              let isFromConnectedAccount = false;
              if (instagramSettings.instagramAccountId && senderId === instagramSettings.instagramAccountId) {
                isFromConnectedAccount = true;
                console.log(`Message is from connected Instagram account: ${senderId}`);
              }

              // Combined echo prevention check
              const isEchoMessage = isFromBusinessAccount || isFromConnectedAccount;
              console.log(`Echo message detection - Business: ${isFromBusinessAccount}, Connected: ${isFromConnectedAccount}, Overall: ${isEchoMessage}`);

              // If it's an echo message, we'll still save it but mark it as not from user

              // Check if contact exists, if not create it
              let contact = await prisma.instagramContact.findFirst({
                where: {
                  organizationId,
                  instagramId: senderId
                }
              });

              if (!contact) {
                // Enhanced validation: Ensure sender ID is not an echo message
                if (isEchoMessage) {
                  console.log(`Skipping contact creation for echo message from: ${senderId} (Business: ${isFromBusinessAccount}, Connected: ${isFromConnectedAccount})`);
                  continue;
                }

                // Validate sender ID format (Instagram IDs should be numeric and reasonable length)
                if (!/^\d{10,20}$/.test(senderId)) {
                  console.log(`Skipping contact creation for invalid sender ID format: ${senderId}`);
                  continue;
                }
                // Create a new contact
                if (!userId) {
                  console.error('No admin user found for organization');
                  return NextResponse.json({ error: 'No admin user found' }, { status: 400 });
                }

                contact = await prisma.instagramContact.create({
                  data: {
                    userId,
                    organizationId,
                    instagramId: senderId,
                    instagramNickname: 'unknown', // We'll update this later when we get the profile
                    stage: 'initial'
                  }
                });
                console.log(`Created new contact: ${contact.id}`);

                // Cache invalidation removed - Instagram contacts no longer cached

                // Fetch profile information for the new contact
                try {
                  if (instagramSettings.instagramToken) {
                    const { getProfile, getConversationHistory } = await import('~/lib/instagram-client');

                    // Get profile information
                    console.log('Fetching profile for new contact...');
                    const profile = await getProfile(
                      senderId,
                      instagramSettings.instagramToken
                    );

                    // Update contact with profile information
                    if (profile) {
                      await prisma.instagramContact.update({
                        where: { id: contact.id },
                        data: {
                          instagramNickname: profile.username || profile.name || 'unknown',
                          avatar: profile.profile_pic || null,
                          followerCount: profile.follower_count || null,
                          isUserFollowBusiness: profile.is_user_follow_business || null,
                          isBusinessFollowUser: profile.is_business_follow_user || null,
                          isVerifiedUser: profile.is_verified_user || null
                        }
                      });
                      console.log(`Updated profile for contact: ${contact.id}`);
                    }

                    // Fetch conversation history
                    console.log('Fetching conversation history for new contact...');
                    const conversationHistory = await getConversationHistory(
                      senderId,
                      instagramSettings.instagramToken
                    );

                    // Save messages to database
                    if (conversationHistory &&
                      conversationHistory.data &&
                      conversationHistory.data[0] &&
                      conversationHistory.data[0].messages &&
                      conversationHistory.data[0].messages.data) {

                      const messages = conversationHistory.data[0].messages.data;
                      console.log(`Found ${messages.length} messages in conversation history`);

                      // Save each message to database
                      for (const message of messages) {
                        // Skip the current message as we'll save it separately
                        if (message.id === messageId) continue;

                        // Check if message already exists
                        const existingMessage = await prisma.instagramMessage.findFirst({
                          where: {
                            contactId: contact.id,
                            messageId: message.id
                          }
                        });

                        if (!existingMessage) {
                          // Determine if message is from user
                          const isFromUser = message.from?.id === senderId;

                          // Check for attachments
                          let mediaUrl;
                          let mediaType;

                          if (message.attachments && message.attachments.data && message.attachments.data.length > 0) {
                            const attachment = message.attachments.data[0];
                            if (attachment.image_data) {
                              mediaUrl = attachment.image_data.url;
                              mediaType = 'image';
                            } else if (attachment.video_data) {
                              mediaUrl = attachment.video_data.url;
                              mediaType = 'video';
                            } else if (attachment.file_url) {
                              mediaUrl = attachment.file_url;
                              mediaType = 'file';
                            }
                          }

                          // Save message to database
                          // Instagram API created_time is Unix timestamp in seconds
                          const messageTimestamp = normalizeTimestamp(message.created_time);

                          console.log(`Processing historical message ${message.id}:`, {
                            created_time: message.created_time,
                            parsed_timestamp: messageTimestamp.toISOString(),
                            content: (message.message || '').substring(0, 50) + '...'
                          });

                          const savedMessage = await prisma.instagramMessage.create({
                            data: {
                              contactId: contact.id,
                              messageId: message.id,
                              content: message.message || '',
                              isFromUser,
                              mediaUrl,
                              mediaType,
                              timestamp: messageTimestamp
                            }
                          });

                          // Automatically transcribe media if present
                          if (mediaUrl && (mediaType === 'image' || mediaType === 'audio') && isFromUser) {
                            try {
                              console.log(`Transcribing ${mediaType} for message: ${savedMessage.id}`);
                              const mediaDescription = await transcribeMedia(mediaUrl, mediaType);

                              // Update message with transcription
                              await prisma.instagramMessage.update({
                                where: { id: savedMessage.id },
                                data: { mediaDescription }
                              });

                              console.log(`Transcription complete for ${mediaType}: ${mediaDescription}`);
                            } catch (transcriptionError) {
                              console.error(`Error transcribing ${mediaType}:`, transcriptionError);
                            }
                          }
                        }
                      }

                      // Update message count
                      await prisma.instagramContact.update({
                        where: { id: contact.id },
                        data: {
                          messageCount: await prisma.instagramMessage.count({
                            where: { contactId: contact.id }
                          })
                        }
                      });

                      console.log(`Synced conversation history for contact: ${contact.id}`);
                    }
                  }
                } catch (error) {
                  console.error('Error fetching profile or conversation history:', error);
                  // Continue with message processing even if profile fetch fails
                }
              } else {
                console.log(`Found existing contact: ${contact.id}`);
              }

              // Check if message already exists to prevent duplicates
              const existingMessage = await prisma.instagramMessage.findFirst({
                where: {
                  contactId: contact.id,
                  messageId
                }
              });

              if (!existingMessage) {
                // Save the message - normalize timestamp for consistent handling
                const messageTimestamp = normalizeTimestamp(timestamp);
                await prisma.instagramMessage.create({
                  data: {
                    contactId: contact.id,
                    messageId,
                    content: messageText,
                    isFromUser: !isEchoMessage, // Mark as not from user if it's an echo message
                    mediaUrl,
                    mediaType,
                    timestamp: messageTimestamp
                  }
                });
                console.log(`Saved message: ${messageId} (from ${isEchoMessage ? 'echo/business account' : 'user'}) at ${messageTimestamp.toISOString()}`);

                // Update message count and lastInteractionAt for USER messages only
                const updateData: any = { messageCount: { increment: 1 } };

                // Only update lastInteractionAt for actual user messages (not echo messages)
                if (!isEchoMessage) {
                  updateData.lastInteractionAt = messageTimestamp;
                  console.log(`Updated lastInteractionAt for contact ${contact.id} to ${messageTimestamp.toISOString()} (user message)`);
                }

                await prisma.instagramContact.update({
                  where: { id: contact.id },
                  data: updateData
                });
              } else {
                console.log(`Message ${messageId} already exists, skipping duplicate`);
              }
              console.log(`Updated message count for contact: ${contact.id}`);

              // Process media if present (in a real implementation, we would transcribe audio and analyze images)
              if (mediaUrl && mediaType) {
                console.log(`Processing media: ${mediaType}`);
                // In a real implementation, we would process the media here
              }

              // Queue the message for AI processing with a delay
              // Only process messages FROM USERS, not messages from the business account
              const contactSettings = await prisma.instagramSettings.findUnique({
                where: { organizationId: contact.organizationId }
              });

              if (contactSettings?.isBotEnabled && !contact.isTakeControl && !isEchoMessage) {
                // Calculate delay in milliseconds
                const minDelay = contactSettings.minResponseTime * 1000;
                const maxDelay = contactSettings.maxResponseTime * 1000;
                const delay = Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;

                // Queue the message for processing with enhanced conversation window support
                try {
                  console.log(`[WEBHOOK] Queueing message from ${senderId} for AI processing with ${delay / 1000}s delay`);
                  console.log(`[WEBHOOK] Message: "${messageText?.substring(0, 100)}${(messageText?.length || 0) > 100 ? '...' : ''}"`);
                  console.log(`[WEBHOOK] Organization: ${contact.organizationId}, Contact: ${contact.id}`);

                  await queueMessage({
                    organizationId: contact.organizationId,
                    senderId,
                    messageId,
                    messageText,
                    mediaUrl,
                    mediaType
                  }, delay, true, timestamp); // Enable conversation windows, pass original timestamp

                  console.log(`[WEBHOOK] Successfully queued message for AI processing: ${contact.id} (media: ${mediaType || 'none'})`);
                } catch (queueError) {
                  console.error(`[WEBHOOK] Failed to queue message for ${contact.id}:`, queueError);

                  // Fallback to legacy queueing if enhanced fails
                  try {
                    await queueMessage({
                      organizationId: contact.organizationId,
                      senderId,
                      messageId,
                      messageText,
                      mediaUrl,
                      mediaType
                    }, delay, false, timestamp); // Disable conversation windows as fallback, pass original timestamp

                    console.log(`Queued message using legacy method for ${contact.id}`);
                  } catch (fallbackError) {
                    console.error(`Both enhanced and legacy queueing failed for ${contact.id}:`, fallbackError);
                  }
                }
              } else {
                if (isEchoMessage) {
                  console.log(`Skipping AI processing for contact: ${contact.id} (echo message from business/connected account)`);
                } else {
                  console.log(`Skipping AI processing for contact: ${contact.id} (bot disabled or manual control)`);
                }
              }
            }
          }
        }
      }
    }

    // Acknowledge the webhook
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing Instagram webhook:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
